"""
Enhanced SwinIR Implementation for Satellite Image Super-Resolution
University Project - Enhancement Techniques Implementation

This module implements 3 key enhancements to the base SwinIR model:
1. Multi-Scale Spatial-Channel Attention (MSCA)
2. Spectral-Aware Feature Fusion (SAFF) 
3. Progressive Residual Dense Connections (PRDC)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
import cv2
import math
from einops import rearrange


class MultiScaleSpatialChannelAttention(nn.Module):
    """
    Multi-Scale Spatial-Channel Attention (MSCA)
    Uses multiple convolution scales with combined spatial and channel attention
    """
    def __init__(self, dim, num_heads=8):
        super().__init__()
        self.dim = dim
        self.num_heads = num_heads
        
        # Multi-scale convolutions
        self.conv1x1 = nn.Conv2d(dim, dim//4, 1, padding=0)
        self.conv3x3 = nn.Conv2d(dim, dim//4, 3, padding=1)
        self.conv5x5 = nn.Conv2d(dim, dim//4, 5, padding=2)
        self.conv7x7 = nn.Conv2d(dim, dim//4, 7, padding=3)
        
        # Channel attention
        self.channel_attention = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(dim, dim//8, 1),
            nn.ReLU(inplace=True),
            nn.Conv2d(dim//8, dim, 1),
            nn.Sigmoid()
        )
        
        # Spatial attention
        self.spatial_attention = nn.Sequential(
            nn.Conv2d(2, 1, 7, padding=3),
            nn.Sigmoid()
        )
        
        self.fusion = nn.Conv2d(dim, dim, 1)
        
    def forward(self, x):
        B, C, H, W = x.shape
        
        # Multi-scale feature extraction
        feat1 = self.conv1x1(x)
        feat3 = self.conv3x3(x)
        feat5 = self.conv5x5(x)
        feat7 = self.conv7x7(x)
        
        # Concatenate multi-scale features
        multi_scale = torch.cat([feat1, feat3, feat5, feat7], dim=1)
        
        # Channel attention
        ca_weight = self.channel_attention(multi_scale)
        multi_scale = multi_scale * ca_weight
        
        # Spatial attention
        avg_pool = torch.mean(multi_scale, dim=1, keepdim=True)
        max_pool, _ = torch.max(multi_scale, dim=1, keepdim=True)
        spatial_input = torch.cat([avg_pool, max_pool], dim=1)
        sa_weight = self.spatial_attention(spatial_input)
        multi_scale = multi_scale * sa_weight
        
        # Final fusion
        output = self.fusion(multi_scale)
        return output + x  # Residual connection


class SpectralAwareFeatureFusion(nn.Module):
    """
    Spectral-Aware Feature Fusion (SAFF)
    Handles multi-spectral satellite data with learnable spectral weights
    """
    def __init__(self, dim, num_spectral_bands=3):
        super().__init__()
        self.dim = dim
        self.num_bands = num_spectral_bands
        
        # Spectral weight learning
        self.spectral_weights = nn.Parameter(torch.ones(num_spectral_bands))
        self.spectral_transform = nn.Conv2d(dim, dim, 1)
        
        # Cross-spectral interaction
        self.cross_spectral = nn.MultiheadAttention(dim, num_heads=8, batch_first=True)
        
        # Feature fusion
        self.fusion_conv = nn.Sequential(
            nn.Conv2d(dim * 2, dim, 3, padding=1),
            nn.ReLU(inplace=True),
            nn.Conv2d(dim, dim, 1)
        )
        
    def forward(self, x):
        B, C, H, W = x.shape
        
        # Apply learnable spectral weights (simulated for RGB)
        spectral_weighted = x * self.spectral_weights.view(1, 3, 1, 1)
        
        # Spectral transformation
        spectral_feat = self.spectral_transform(spectral_weighted)
        
        # Cross-spectral attention
        # Reshape for attention: (B, HW, C)
        x_flat = x.view(B, C, H*W).transpose(1, 2)
        spectral_flat = spectral_feat.view(B, C, H*W).transpose(1, 2)
        
        attended_feat, _ = self.cross_spectral(spectral_flat, x_flat, x_flat)
        attended_feat = attended_feat.transpose(1, 2).view(B, C, H, W)
        
        # Feature fusion
        fused = torch.cat([x, attended_feat], dim=1)
        output = self.fusion_conv(fused)
        
        return output


class ProgressiveResidualDenseConnection(nn.Module):
    """
    Progressive Residual Dense Connections (PRDC)
    Dense connections within residual blocks for better feature propagation
    """
    def __init__(self, dim, growth_rate=32, num_layers=4):
        super().__init__()
        self.num_layers = num_layers
        self.growth_rate = growth_rate
        
        # Dense layers
        self.dense_layers = nn.ModuleList()
        for i in range(num_layers):
            layer = nn.Sequential(
                nn.Conv2d(dim + i * growth_rate, growth_rate, 3, padding=1),
                nn.ReLU(inplace=True)
            )
            self.dense_layers.append(layer)
        
        # Feature fusion
        self.fusion = nn.Conv2d(dim + num_layers * growth_rate, dim, 1)
        
        # Progressive scaling
        self.progressive_scale = nn.Parameter(torch.linspace(0.1, 1.0, num_layers))
        
    def forward(self, x):
        features = [x]
        
        for i, layer in enumerate(self.dense_layers):
            # Concatenate all previous features
            concat_feat = torch.cat(features, dim=1)
            
            # Apply dense layer with progressive scaling
            new_feat = layer(concat_feat) * self.progressive_scale[i]
            features.append(new_feat)
        
        # Final fusion
        all_features = torch.cat(features, dim=1)
        output = self.fusion(all_features)
        
        return output + x  # Residual connection


class EnhancedSwinIRBlock(nn.Module):
    """
    Enhanced SwinIR Block with all three improvements
    """
    def __init__(self, dim, num_heads=8):
        super().__init__()
        
        # Core enhancements
        self.msca = MultiScaleSpatialChannelAttention(dim, num_heads)
        self.saff = SpectralAwareFeatureFusion(dim)
        self.prdc = ProgressiveResidualDenseConnection(dim)
        
        # Layer normalization
        self.norm1 = nn.LayerNorm(dim)
        self.norm2 = nn.LayerNorm(dim)
        self.norm3 = nn.LayerNorm(dim)
        
    def forward(self, x):
        B, C, H, W = x.shape
        
        # Apply MSCA
        x = x + self.msca(x)
        x_norm1 = self.norm1(x.view(B, C, -1).transpose(1, 2)).transpose(1, 2).view(B, C, H, W)
        
        # Apply SAFF
        x = x_norm1 + self.saff(x_norm1)
        x_norm2 = self.norm2(x.view(B, C, -1).transpose(1, 2)).transpose(1, 2).view(B, C, H, W)
        
        # Apply PRDC
        x = x_norm2 + self.prdc(x_norm2)
        x_norm3 = self.norm3(x.view(B, C, -1).transpose(1, 2)).transpose(1, 2).view(B, C, H, W)
        
        return x_norm3


class EnhancedSwinIR(nn.Module):
    """
    Enhanced SwinIR Model with MSCA, SAFF, and PRDC improvements
    """
    def __init__(self, img_size=32, patch_size=1, in_chans=3, embed_dim=96,
                 depths=[6, 6, 6, 6], num_heads=[6, 6, 6, 6], window_size=8,
                 mlp_ratio=2., upscale=4, img_range=1., upsampler='pixelshuffle'):
        super().__init__()
        
        self.img_size = img_size
        self.patch_size = patch_size
        self.in_chans = in_chans
        self.embed_dim = embed_dim
        self.upscale = upscale
        self.img_range = img_range
        
        # Shallow feature extraction
        self.conv_first = nn.Conv2d(in_chans, embed_dim, 3, 1, 1)
        
        # Enhanced blocks
        self.enhanced_blocks = nn.ModuleList([
            EnhancedSwinIRBlock(embed_dim, num_heads[0]) for _ in range(sum(depths))
        ])
        
        # Feature fusion
        self.conv_after_body = nn.Conv2d(embed_dim, embed_dim, 3, 1, 1)
        
        # Upsampling
        if upsampler == 'pixelshuffle':
            self.upsampler = nn.Sequential(
                nn.Conv2d(embed_dim, embed_dim * (upscale ** 2), 3, 1, 1),
                nn.PixelShuffle(upscale)
            )
        
        # Final output
        self.conv_last = nn.Conv2d(embed_dim, in_chans, 3, 1, 1)
        
    def forward(self, x):
        # Normalize input
        x = x / self.img_range
        
        # Shallow feature extraction
        x = self.conv_first(x)
        residual = x
        
        # Enhanced processing
        for block in self.enhanced_blocks:
            x = block(x)
        
        # Feature fusion
        x = self.conv_after_body(x) + residual
        
        # Upsampling
        x = self.upsampler(x)
        
        # Final output
        x = self.conv_last(x)
        
        # Denormalize
        x = x * self.img_range
        
        return x


def create_image_patches(image, patch_size=128, num_patches=4):
    """Create random patches from an image for testing"""
    h, w = image.shape[:2]
    patches = []
    
    for _ in range(num_patches):
        # Random crop
        start_h = np.random.randint(0, max(1, h - patch_size))
        start_w = np.random.randint(0, max(1, w - patch_size))
        
        patch = image[start_h:start_h+patch_size, start_w:start_w+patch_size]
        
        # Ensure patch is the right size
        if patch.shape[0] != patch_size or patch.shape[1] != patch_size:
            patch = cv2.resize(patch, (patch_size, patch_size))
        
        patches.append(patch)
    
    return patches


def enhance_resolution(lr_image, model, device='cpu'):
    """Enhance resolution of a single image using the model"""
    # Convert to tensor
    if isinstance(lr_image, np.ndarray):
        lr_tensor = torch.from_numpy(lr_image.transpose(2, 0, 1)).float().unsqueeze(0) / 255.0
    else:
        lr_tensor = lr_image
    
    lr_tensor = lr_tensor.to(device)
    
    # Inference
    with torch.no_grad():
        sr_tensor = model(lr_tensor)
        sr_image = (sr_tensor.squeeze().cpu().numpy().transpose(1, 2, 0) * 255).astype(np.uint8)
        sr_image = np.clip(sr_image, 0, 255)
    
    return sr_image
