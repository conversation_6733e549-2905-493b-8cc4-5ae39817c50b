"""
Image De-enhancer Utility
Creates low-resolution versions of high-resolution satellite images for testing

This utility helps create proper test datasets by:
1. Taking high-resolution satellite images
2. Applying realistic degradation (blur, noise, downsampling)
3. Creating 4x downsampled versions (e.g., 128x128 -> 32x32)
4. Simulating real-world satellite image quality loss
"""

import cv2
import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt
from PIL import Image, ImageFilter
import os


class SatelliteImageDeenhancer:
    """
    Creates realistic low-resolution versions of satellite images
    Simulates the degradation from NAIP (2.5m) to Sentinel-2 (10m) resolution
    """
    
    def __init__(self, scale_factor=4):
        self.scale_factor = scale_factor
        
    def add_gaussian_blur(self, image, kernel_size=5, sigma=1.5):
        """Add Gaussian blur to simulate atmospheric effects"""
        return cv2.GaussianBlur(image, (kernel_size, kernel_size), sigma)
    
    def add_noise(self, image, noise_level=10):
        """Add realistic noise to simulate sensor limitations"""
        noise = np.random.normal(0, noise_level, image.shape).astype(np.float32)
        noisy_image = image.astype(np.float32) + noise
        return np.clip(noisy_image, 0, 255).astype(np.uint8)
    
    def add_compression_artifacts(self, image, quality=85):
        """Add JPEG compression artifacts"""
        # Convert to PIL for JPEG compression
        pil_image = Image.fromarray(image)
        
        # Save and reload with compression
        import io
        buffer = io.BytesIO()
        pil_image.save(buffer, format='JPEG', quality=quality)
        buffer.seek(0)
        compressed_image = Image.open(buffer)
        
        return np.array(compressed_image)
    
    def simulate_atmospheric_effects(self, image, haze_strength=0.1):
        """Simulate atmospheric haze and scattering"""
        # Create haze effect
        haze = np.ones_like(image, dtype=np.float32) * 255 * haze_strength
        hazy_image = image.astype(np.float32) * (1 - haze_strength) + haze
        return np.clip(hazy_image, 0, 255).astype(np.uint8)
    
    def downsample_realistic(self, image, target_size):
        """Realistic downsampling with anti-aliasing"""
        # Apply anti-aliasing filter before downsampling
        blurred = self.add_gaussian_blur(image, kernel_size=3, sigma=0.8)
        
        # Downsample using high-quality interpolation
        downsampled = cv2.resize(blurred, target_size, interpolation=cv2.INTER_AREA)
        
        return downsampled
    
    def create_lr_version(self, hr_image, degradation_level='medium'):
        """
        Create low-resolution version with realistic degradation
        
        Args:
            hr_image: High-resolution input image (numpy array)
            degradation_level: 'light', 'medium', 'heavy'
        
        Returns:
            lr_image: Low-resolution degraded image
        """
        
        # Define degradation parameters
        degradation_params = {
            'light': {
                'blur_sigma': 0.8,
                'noise_level': 5,
                'compression_quality': 95,
                'haze_strength': 0.05
            },
            'medium': {
                'blur_sigma': 1.2,
                'noise_level': 10,
                'compression_quality': 85,
                'haze_strength': 0.1
            },
            'heavy': {
                'blur_sigma': 1.8,
                'noise_level': 20,
                'compression_quality': 70,
                'haze_strength': 0.15
            }
        }
        
        params = degradation_params[degradation_level]
        
        # Start with original image
        degraded = hr_image.copy()
        
        # Apply atmospheric effects
        degraded = self.simulate_atmospheric_effects(degraded, params['haze_strength'])
        
        # Add blur (atmospheric + sensor)
        degraded = self.add_gaussian_blur(degraded, kernel_size=5, sigma=params['blur_sigma'])
        
        # Add compression artifacts
        degraded = self.add_compression_artifacts(degraded, params['compression_quality'])
        
        # Add sensor noise
        degraded = self.add_noise(degraded, params['noise_level'])
        
        # Final downsampling
        h, w = hr_image.shape[:2]
        target_size = (w // self.scale_factor, h // self.scale_factor)
        lr_image = self.downsample_realistic(degraded, target_size)
        
        return lr_image
    
    def create_test_dataset(self, input_image_path, output_dir, patch_size=128, num_patches=8):
        """
        Create a test dataset from a single high-resolution image
        
        Args:
            input_image_path: Path to high-resolution satellite image
            output_dir: Directory to save HR/LR patch pairs
            patch_size: Size of HR patches (LR will be patch_size/scale_factor)
            num_patches: Number of patch pairs to create
        """
        
        # Create output directories
        hr_dir = os.path.join(output_dir, 'HR')
        lr_dir = os.path.join(output_dir, 'LR')
        os.makedirs(hr_dir, exist_ok=True)
        os.makedirs(lr_dir, exist_ok=True)
        
        # Load input image
        image = cv2.imread(input_image_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        print(f"Input image shape: {image.shape}")
        print(f"Creating {num_patches} patch pairs...")
        
        h, w = image.shape[:2]
        
        for i in range(num_patches):
            # Random crop for HR patch
            start_h = np.random.randint(0, max(1, h - patch_size))
            start_w = np.random.randint(0, max(1, w - patch_size))
            
            hr_patch = image[start_h:start_h+patch_size, start_w:start_w+patch_size]
            
            # Ensure patch is correct size
            if hr_patch.shape[0] != patch_size or hr_patch.shape[1] != patch_size:
                hr_patch = cv2.resize(hr_patch, (patch_size, patch_size))
            
            # Create LR version with different degradation levels
            degradation_levels = ['light', 'medium', 'heavy']
            degradation = degradation_levels[i % len(degradation_levels)]
            
            lr_patch = self.create_lr_version(hr_patch, degradation)
            
            # Save patches
            hr_filename = f'hr_patch_{i:03d}_{degradation}.png'
            lr_filename = f'lr_patch_{i:03d}_{degradation}.png'
            
            cv2.imwrite(os.path.join(hr_dir, hr_filename), 
                       cv2.cvtColor(hr_patch, cv2.COLOR_RGB2BGR))
            cv2.imwrite(os.path.join(lr_dir, lr_filename), 
                       cv2.cvtColor(lr_patch, cv2.COLOR_RGB2BGR))
            
            print(f"  Created patch {i+1}/{num_patches}: {degradation} degradation")
        
        print(f"✅ Dataset created in {output_dir}")
        return hr_dir, lr_dir
    
    def visualize_degradation(self, hr_image, save_path=None):
        """Visualize the degradation process"""
        
        # Create LR versions with different degradation levels
        lr_light = self.create_lr_version(hr_image, 'light')
        lr_medium = self.create_lr_version(hr_image, 'medium')
        lr_heavy = self.create_lr_version(hr_image, 'heavy')
        
        # Upscale LR images for comparison
        h, w = hr_image.shape[:2]
        lr_light_up = cv2.resize(lr_light, (w, h), interpolation=cv2.INTER_CUBIC)
        lr_medium_up = cv2.resize(lr_medium, (w, h), interpolation=cv2.INTER_CUBIC)
        lr_heavy_up = cv2.resize(lr_heavy, (w, h), interpolation=cv2.INTER_CUBIC)
        
        # Create comparison plot
        fig, axes = plt.subplots(2, 4, figsize=(16, 8))
        
        # Top row: Original sizes
        axes[0, 0].imshow(hr_image)
        axes[0, 0].set_title(f'Original HR\\n{hr_image.shape[1]}x{hr_image.shape[0]}')
        axes[0, 0].axis('off')
        
        axes[0, 1].imshow(lr_light)
        axes[0, 1].set_title(f'Light Degradation\\n{lr_light.shape[1]}x{lr_light.shape[0]}')
        axes[0, 1].axis('off')
        
        axes[0, 2].imshow(lr_medium)
        axes[0, 2].set_title(f'Medium Degradation\\n{lr_medium.shape[1]}x{lr_medium.shape[0]}')
        axes[0, 2].axis('off')
        
        axes[0, 3].imshow(lr_heavy)
        axes[0, 3].set_title(f'Heavy Degradation\\n{lr_heavy.shape[1]}x{lr_heavy.shape[0]}')
        axes[0, 3].axis('off')
        
        # Bottom row: Upscaled for comparison
        axes[1, 0].imshow(hr_image)
        axes[1, 0].set_title('Ground Truth')
        axes[1, 0].axis('off')
        
        axes[1, 1].imshow(lr_light_up)
        axes[1, 1].set_title('Light (Upscaled)')
        axes[1, 1].axis('off')
        
        axes[1, 2].imshow(lr_medium_up)
        axes[1, 2].set_title('Medium (Upscaled)')
        axes[1, 2].axis('off')
        
        axes[1, 3].imshow(lr_heavy_up)
        axes[1, 3].set_title('Heavy (Upscaled)')
        axes[1, 3].axis('off')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Degradation comparison saved to: {save_path}")

        plt.close()  # Close figure to free memory


def main():
    """Example usage of the de-enhancer"""
    
    # Initialize de-enhancer
    deenhancer = SatelliteImageDeenhancer(scale_factor=4)
    
    # Input image path
    input_path = 'data/input/sentinel_sample.jpg'
    
    if os.path.exists(input_path):
        # Load and process image
        image = cv2.imread(input_path)
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Create a sample patch for demonstration
        patch = image[100:228, 100:228]  # 128x128 patch
        
        print("🔧 Demonstrating image de-enhancement...")
        
        # Visualize degradation process
        deenhancer.visualize_degradation(patch, 'data/output/degradation_demo.png')
        
        # Create test dataset
        print("\n📁 Creating test dataset...")
        hr_dir, lr_dir = deenhancer.create_test_dataset(
            input_path, 
            'data/test_dataset', 
            patch_size=128, 
            num_patches=6
        )
        
        print(f"\n✅ Test dataset created:")
        print(f"  HR patches: {hr_dir}")
        print(f"  LR patches: {lr_dir}")
        
    else:
        print(f"❌ Input image not found: {input_path}")
        print("Please ensure the sample image exists in data/input/")


if __name__ == "__main__":
    main()
