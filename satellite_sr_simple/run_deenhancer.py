#!/usr/bin/env python3
"""
Quick script to run the image de-enhancer and create test data
Run this before the main demonstration to prepare proper LR/HR pairs
"""

import os
import sys
import cv2
import numpy as np
import matplotlib
matplotlib.use('Agg')  # Use non-interactive backend
import matplotlib.pyplot as plt

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from image_deenhancer import SatelliteImageDeenhancer


def main():
    print("🛰️ Satellite Image De-enhancer")
    print("=" * 50)
    
    # Check if input image exists
    input_path = 'data/input/sentinel_sample.jpg'
    if not os.path.exists(input_path):
        print(f"❌ Input image not found: {input_path}")
        print("Please ensure the sample image exists!")
        return
    
    # Create output directory
    os.makedirs('data/output', exist_ok=True)
    
    # Initialize de-enhancer (4x downsampling: 128x128 -> 32x32)
    deenhancer = SatelliteImageDeenhancer(scale_factor=4)
    
    # Load the sample image
    print(f"📁 Loading image: {input_path}")
    image = cv2.imread(input_path)
    image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    print(f"   Image shape: {image.shape}")
    
    # Extract a good patch for demonstration
    h, w = image.shape[:2]
    
    # Take center patch to avoid edges
    center_h, center_w = h // 2, w // 2
    patch_size = 128
    start_h = center_h - patch_size // 2
    start_w = center_w - patch_size // 2
    
    hr_patch = image[start_h:start_h+patch_size, start_w:start_w+patch_size]
    print(f"   Extracted HR patch: {hr_patch.shape}")
    
    # Create LR versions with different degradation levels
    print("\n🔧 Creating degraded LR versions...")
    
    lr_light = deenhancer.create_lr_version(hr_patch, 'light')
    lr_medium = deenhancer.create_lr_version(hr_patch, 'medium')
    lr_heavy = deenhancer.create_lr_version(hr_patch, 'heavy')
    
    print(f"   Light degradation: {lr_light.shape}")
    print(f"   Medium degradation: {lr_medium.shape}")
    print(f"   Heavy degradation: {lr_heavy.shape}")
    
    # Save the patches for use in the main demo
    print("\n💾 Saving patches for demo...")
    
    # Save HR patch
    cv2.imwrite('data/input/hr_demo_patch.png', 
                cv2.cvtColor(hr_patch, cv2.COLOR_RGB2BGR))
    
    # Save LR patches
    cv2.imwrite('data/input/lr_demo_light.png', 
                cv2.cvtColor(lr_light, cv2.COLOR_RGB2BGR))
    cv2.imwrite('data/input/lr_demo_medium.png', 
                cv2.cvtColor(lr_medium, cv2.COLOR_RGB2BGR))
    cv2.imwrite('data/input/lr_demo_heavy.png', 
                cv2.cvtColor(lr_heavy, cv2.COLOR_RGB2BGR))
    
    # Create visualization
    print("\n📊 Creating degradation visualization...")
    
    # Upscale LR images for comparison
    lr_light_up = cv2.resize(lr_light, (patch_size, patch_size), interpolation=cv2.INTER_CUBIC)
    lr_medium_up = cv2.resize(lr_medium, (patch_size, patch_size), interpolation=cv2.INTER_CUBIC)
    lr_heavy_up = cv2.resize(lr_heavy, (patch_size, patch_size), interpolation=cv2.INTER_CUBIC)
    
    # Create comparison plot
    fig, axes = plt.subplots(2, 4, figsize=(16, 8))
    
    # Top row: Actual sizes
    axes[0, 0].imshow(hr_patch)
    axes[0, 0].set_title(f'Original HR\n{hr_patch.shape[1]}×{hr_patch.shape[0]}', fontsize=10)
    axes[0, 0].axis('off')
    
    axes[0, 1].imshow(lr_light)
    axes[0, 1].set_title(f'Light Degradation\n{lr_light.shape[1]}×{lr_light.shape[0]}', fontsize=10)
    axes[0, 1].axis('off')
    
    axes[0, 2].imshow(lr_medium)
    axes[0, 2].set_title(f'Medium Degradation\n{lr_medium.shape[1]}×{lr_medium.shape[0]}', fontsize=10)
    axes[0, 2].axis('off')
    
    axes[0, 3].imshow(lr_heavy)
    axes[0, 3].set_title(f'Heavy Degradation\n{lr_heavy.shape[1]}×{lr_heavy.shape[0]}', fontsize=10)
    axes[0, 3].axis('off')
    
    # Bottom row: Upscaled for comparison
    axes[1, 0].imshow(hr_patch)
    axes[1, 0].set_title('Ground Truth\n(Original)', fontsize=10, fontweight='bold')
    axes[1, 0].axis('off')
    
    axes[1, 1].imshow(lr_light_up)
    axes[1, 1].set_title('Light → Upscaled\n(Bicubic)', fontsize=10)
    axes[1, 1].axis('off')
    
    axes[1, 2].imshow(lr_medium_up)
    axes[1, 2].set_title('Medium → Upscaled\n(Bicubic)', fontsize=10)
    axes[1, 2].axis('off')
    
    axes[1, 3].imshow(lr_heavy_up)
    axes[1, 3].set_title('Heavy → Upscaled\n(Bicubic)', fontsize=10)
    axes[1, 3].axis('off')
    
    plt.suptitle('Satellite Image Degradation Process\n4x Downsampling (128×128 → 32×32)', 
                 fontsize=14, fontweight='bold')
    plt.tight_layout()
    
    # Save visualization
    output_path = 'data/output/degradation_process.png'
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    plt.close()  # Close figure to free memory

    print(f"   Saved visualization: {output_path}")
    
    # Create additional test patches for the demo
    print("\n📦 Creating additional test patches...")
    
    # Create 4 random patches for comprehensive testing
    test_patches_hr = []
    test_patches_lr = []
    
    for i in range(4):
        # Random location
        start_h = np.random.randint(0, max(1, h - patch_size))
        start_w = np.random.randint(0, max(1, w - patch_size))
        
        # Extract HR patch
        hr_test = image[start_h:start_h+patch_size, start_w:start_w+patch_size]
        
        # Create LR version (use medium degradation for consistency)
        lr_test = deenhancer.create_lr_version(hr_test, 'medium')
        
        test_patches_hr.append(hr_test)
        test_patches_lr.append(lr_test)
        
        # Save individual patches
        cv2.imwrite(f'data/input/test_hr_{i}.png', 
                    cv2.cvtColor(hr_test, cv2.COLOR_RGB2BGR))
        cv2.imwrite(f'data/input/test_lr_{i}.png', 
                    cv2.cvtColor(lr_test, cv2.COLOR_RGB2BGR))
    
    print(f"   Created {len(test_patches_hr)} test patch pairs")
    
    # Summary
    print("\n✅ De-enhancement Complete!")
    print("=" * 50)
    print("📁 Files created:")
    print("   • data/input/hr_demo_patch.png (128×128)")
    print("   • data/input/lr_demo_*.png (32×32)")
    print("   • data/input/test_hr_*.png (4 patches)")
    print("   • data/input/test_lr_*.png (4 patches)")
    print("   • data/output/degradation_process.png")
    print("\n🚀 Ready for main demonstration!")
    print("   Run the Jupyter notebook: satellite_sr_demo.ipynb")


if __name__ == "__main__":
    main()
