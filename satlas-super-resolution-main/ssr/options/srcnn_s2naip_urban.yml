# General Settings
name: srcnn_s2naip
model_type: L2Model
scale: 4
num_gpu: auto
manual_seed: 0

# Dataset and Dataloader Settings
datasets:
  train:
    name: train
    type: S2NAIPDataset

    sentinel2_path:  super_resolution_2023-12-08/train_urban_set/sentinel2
    naip_path:  super_resolution_2023-12-08/train_urban_set/naip

    tile_weights:  super_resolution_2023-12-08/train_tile_weights/urban_set_weights.json

    use_shuffle: False  # shuffle must be false if using tile_weights

    num_worker_per_gpu: 8
    batch_size_per_gpu: 32

    n_s2_images: 8

    use_3d: True

    io_backend:
      type: disk

  val:
     name: validation
     type: S2NAIPDataset

     sentinel2_path:  super_resolution_2023-12-08/small_val_set/sentinel2
     naip_path:  super_resolution_2023-12-08/small_val_set/naip

     use_shuffle: False

     n_s2_images: 8
  
     use_3d: True

     io_backend:
       type: disk

test_datasets:
  test:
     name: test
     type: S2NAIPDataset

     phase: test
     scale: 4

     sentinel2_path: super_resolution_2023-12-08/small_val_set/sentinel2
     naip_path: super_resolution_2023-12-08/small_val_set/naip

     use_shuffle: False

     n_s2_images: 8

     use_3d: True

     io_backend:
       type: disk

# Network Structure
network_g:
  type: SRCNN
  in_channels: 3
  mask_channels: 0 
  hidden_channels: 128
  out_channels: 3
  kernel_size: 3
  residual_layers: 1
  output_size: 128
  revisits: 8
  zoom_factor: 4
  sr_kernel_size: 1

# Load in existing weights to the generator and discriminator
# Uncomment pretrain_network_g and pretrain_network_d and add paths to your weights
path:
  #pretrain_network_g: experiments/sample_net_g.pth 
  param_key_g: params_ema
  strict_load_g: true
  resume_state: ~

# Training Settings
train:
  optim_g:
    type: Adam
    lr: !!float 1e-4
    weight_decay: 0
    betas: [0.9, 0.99]

  scheduler:
    type: MultiStepLR
    milestones: [400000]
    gamma: 0.5

  total_iter: 100000000
  warmup_iter: -1  # no warm up

  # Losses are actually defined in ssr_l2_model.py, but basicsr requires some definition
  pixel_opt:
    type: L1Loss
    loss_weight: 1.0
    reduction: mean

# Validation Settings
val:
   val_freq: !!float 5e3
   save_img: True

   metrics:
     psnr: 
       type: calculate_psnr
       crop_border: 4
       test_y_channel: false
     ssim: 
       type: calculate_ssim
       crop_border: 4
       test_y_channel: false

# Testing Settings
test:
  save_img: True

  metrics:
     psnr:
       type: calculate_psnr
       crop_border: 4
       test_y_channel: false
     ssim:
       type: calculate_ssim
       crop_border: 4
       test_y_channel: false
     cpsnr:
       type: calculate_cpsnr
       crop_border: 4
       test_y_channel: false
     lpips:
       type: calculate_lpips
       lpips_model: vgg
     clipscore:
       type: calculate_clipscore
       clip_model: siglip-ViT-SO400M-14  # see ssr/metrics/clipscore.py for currently supported models

# Logging Settings
logger:
  print_freq: 100
  save_checkpoint_freq: !!float 5e3 
  use_tb_logger: true
  wandb:
    project: ~
    resume_id: ~

# Dist Training Settings
dist_params:
  backend: nccl
  port: 29500
