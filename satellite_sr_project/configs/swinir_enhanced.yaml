# Enhanced SwinIR Configuration for Satellite Imagery Super-Resolution

# Model Configuration
model:
  name: "enhanced_swinir"
  architecture: "swinir"

  # Base SwinIR parameters
  img_size: 64
  patch_size: 1
  in_chans: 3
  embed_dim: 180
  depths: [6, 6, 6, 6, 6, 6]
  num_heads: [6, 6, 6, 6, 6, 6]
  window_size: 8
  mlp_ratio: 2.0
  upscale: 4
  img_range: 1.0
  upsampler: "pixelshuffle"
  resi_connection: "1conv"

  # Enhancement modules
  enhancements:
    msca:
      enabled: true
      reduction: 16
      scales: [1, 3, 5, 7]
    saff:
      enabled: true
      num_bands: 3  # RGB, change to 13 for Sentinel-2
      spectral_loss_weight: 0.1
    prdc:
      enabled: true
      growth_rate: 32
      num_blocks: 3

# Training Configuration
training:
  # Basic parameters
  batch_size: 16
  num_epochs: 300
  learning_rate: 2e-4
  weight_decay: 0.0

  # Scheduler
  scheduler:
    type: "cosine"
    warmup_epochs: 10
    min_lr: 1e-7

  # Loss function
  loss:
    type: "combined"
    l1_weight: 1.0
    perceptual_weight: 0.1
    spectral_weight: 0.05

  # Optimization
  optimizer: "adamw"
  gradient_clip: 0.01

  # Checkpointing
  save_freq: 10
  val_freq: 5

# Data Configuration
data:
  # Dataset paths
  train_hr_path: "data/train/hr"
  train_lr_path: "data/train/lr"
  val_hr_path: "data/val/hr"
  val_lr_path: "data/val/lr"
  test_hr_path: "data/test/hr"
  test_lr_path: "data/test/lr"

  # Data parameters
  scale_factor: 4
  patch_size: 256
  batch_size: 16
  num_workers: 8

  # Augmentation
  augmentation:
    enabled: true
    horizontal_flip: 0.5
    vertical_flip: 0.5
    rotation: 0.3
    color_jitter: 0.1

  # Preprocessing
  normalize: true
  mean: [0.485, 0.456, 0.406]
  std: [0.229, 0.224, 0.225]

# Evaluation Configuration
evaluation:
  metrics: ["psnr", "ssim", "lpips"]
  save_images: true
  num_test_images: 100

# Logging Configuration
logging:
  use_wandb: true
  use_tensorboard: true
  log_freq: 100
  project_name: "satellite_sr"
  experiment_name: "enhanced_swinir"

# Hardware Configuration
hardware:
  device: "cuda"
  mixed_precision: true
  num_gpus: 1

# Paths
paths:
  checkpoint_dir: "models/checkpoints"
  results_dir: "results"
  logs_dir: "logs"